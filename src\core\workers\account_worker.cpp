#include "account_worker.h"
#include "../../legacy/network/ultrafasttls.h"
#include "../../legacy/api/api_constants.h"
#include "../utils/logger.h"
#include <QDateTime>
#include <QCryptographicHash>
#include <QUrlQuery>
#include <QUrl>

AccountWorker::AccountWorker(const QString& accountId, QObject* parent)
    : QObject(parent), m_accountId(accountId)
{
    // 创建固定的UltraFastTLS实例
    m_tlsInstance = new UltraFastTLS();
    m_tlsInstance->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
    m_tlsInstance->setQuietMode(true);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("AccountWorker created for account: %1").arg(accountId));
}

AccountWorker::~AccountWorker()
{
    delete m_tlsInstance;
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("AccountWorker destroyed for account: %1").arg(m_accountId));
}

void AccountWorker::setupAccount(const QString& username, const QString& password,
                                const QString& token, const QString& userId)
{
    m_username = username;
    m_password = password;
    m_token = token;
    m_userId = userId;
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("Account %1 configured").arg(username));
}

void AccountWorker::setupProxy(const QString& host, int port, const QString& type,
                              const QString& user, const QString& pass)
{
    if (!host.isEmpty() && port > 0) {
        // 只设置一次代理，后续重复使用
        m_tlsInstance->setProxy(host, port, type, user, pass);
        m_proxyConfigured = true;
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, 
                    QString("Proxy configured for account %1: %2:%3 (%4)")
                    .arg(m_accountId, host).arg(port).arg(type));
    }
}

void AccountWorker::setGameId(const QString& gameId)
{
    m_gameId = gameId;
}

void AccountWorker::setPageSize(int pageSize)
{
    m_pageSize = pageSize;
}

void AccountWorker::setPriceFilter(const QString& priceStr)
{
    m_priceFilter = priceStr;
}

void AccountWorker::setFocusFlag(int focusFlag)
{
    m_focusFlag = focusFlag;
}

void AccountWorker::startRefresh()
{
    try {
        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 开始刷新订单").arg(m_accountId));
        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 认证信息 - Token前10位: %2, UserId: %3")
                     .arg(m_accountId)
                     .arg(m_token.isEmpty() ? "空" : m_token.left(10))
                     .arg(m_userId.isEmpty() ? "空" : m_userId));
        
        // 构建请求参数
        QList<QPair<QString, QString>> params = {
            {"IsPub", "0"},
            {"GameID", m_gameId},
            {"ZoneID", "0"},
            {"ServerID", "0"},
            {"SearchStr", ""},
            {"STier", ""},
            {"ETier", ""},
            {"Sort_Str", ""},
            {"PageIndex", "1"},
            {"PageSize", QString::number(m_pageSize)},
            {"PriceStr", m_priceFilter},
            {"PubCancel", "0"},
            {"SettleHour", "0"},
            {"FilterType", "0"},
            {"PGType", "0"},
            {"IsFocused", QString::number(m_focusFlag)},
            {"OrderType", "0"},
            {"PubRecommend", "0"},
            {"Score1", "0"},
            {"Score2", "0"},
            {"UserID", m_userId},
            {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
            {"Ver", "1.0"},
            {"AppVer", "5.0.6"},
            {"AppOS", "WebApp%20IOS"},
            {"AppID", "webapp"}
        };
        
        // 添加token
        if (!m_token.isEmpty()) {
            params.append({"Token", m_token});
        }
        
        // 计算签名
        QString action = "LevelOrderList";
        QString sign = calculateSign(action, params, m_token);
        params.append({"Sign", sign});
        
        // 构建POST数据
        QString postData = buildPostData(params);
        QString url = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);

        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 请求URL: %2").arg(m_accountId, url));
        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] POST数据长度: %2").arg(m_accountId).arg(postData.length()));
        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] POST数据前200字符: %2").arg(m_accountId, postData.left(200)));

        // 检查TLS实例状态
        if (!m_tlsInstance) {
            NEW_LOG_ERROR(NewLogCategory::ORDER, QString("🔍 [%1] TLS实例为空！").arg(m_accountId));
            emit refreshError(m_accountId, "TLS实例为空");
            return;
        }

        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 开始执行网络请求...").arg(m_accountId));

        // 使用固定的TLS实例执行请求（连接复用）
        QString response = m_tlsInstance->executeRequest(url, postData);

        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 网络请求完成，响应长度: %2").arg(m_accountId).arg(response.length()));

        if (!response.isEmpty()) {
            NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 [%1] 响应前200字符: %2").arg(m_accountId, response.left(200)));
            NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("Account %1 refresh completed").arg(m_accountId));
            emit refreshCompleted(m_accountId, response, true);
        } else {
            NEW_LOG_ERROR(NewLogCategory::ORDER, QString("🔍 [%1] 响应为空！可能原因：网络连接失败、服务器错误、代理问题").arg(m_accountId));
            emit refreshError(m_accountId, "响应为空");
        }
        
    } catch (const std::exception& e) {
        QString error = QString("异常: %1").arg(e.what());
        NEW_LOG_ERROR(NewLogCategory::ORDER, QString("Account %1 refresh exception: %2").arg(m_accountId, error));
        emit refreshError(m_accountId, error);
    } catch (...) {
        QString error = "未知异常";
        NEW_LOG_ERROR(NewLogCategory::ORDER, QString("Account %1 refresh unknown exception").arg(m_accountId));
        emit refreshError(m_accountId, error);
    }
}

QString AccountWorker::buildPostData(const QList<QPair<QString, QString>>& params)
{
    QStringList parts;
    for (const auto& param : params) {
        parts.append(QString("%1=%2").arg(param.first, param.second));
    }
    return parts.join("&");
}

QString AccountWorker::calculateSign(const QString& action, const QList<QPair<QString, QString>>& params, const QString& token)
{
    // 使用与OrderAPI相同的签名算法
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";

    // 构建POST数据字符串
    QString data = buildPostData(params);

    // 解析参数并构建签名字符串
    QStringList pairs = data.split('&');
    QString unescapedData;
    for (const QString& pair : pairs) {
        QStringList keyValue = pair.split('=');
        if (keyValue.size() == 2) {
            QString key = QUrl::fromPercentEncoding(keyValue[0].toUtf8());
            QString value = QUrl::fromPercentEncoding(keyValue[1].toUtf8());
            unescapedData += key + value;
        }
    }

    QString signStr = secretKey + action + unescapedData + token;

    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(signStr.toUtf8());
    return hash.result().toHex().toLower();
}
