#include "simple_refresh_manager.h"
#include "../workers/account_worker.h"
#include "../utils/logger.h"
#include <QMetaObject>

SimpleRefreshManager::SimpleRefreshManager(QObject* parent)
    : QObject(parent)
{
    m_intervalTimer = new QTimer(this);
    m_intervalTimer->setSingleShot(true);
    connect(m_intervalTimer, &QTimer::timeout, this, &SimpleRefreshManager::onIntervalTimeout);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "SimpleRefreshManager created");
}

SimpleRefreshManager::~SimpleRefreshManager()
{
    // 停止刷新
    stopBatchRefresh();
    
    // 清理所有线程和Worker
    for (auto& unit : m_accounts) {
        unit.thread->quit();
        if (!unit.thread->wait(5000)) {  // 等待最多5秒
            NEW_LOG_WARNING(NewLogCategory::SYSTEM, 
                           QString("Thread for account %1 did not quit gracefully").arg(unit.accountId));
            unit.thread->terminate();
            unit.thread->wait(1000);
        }
        delete unit.worker;
        delete unit.thread;
    }
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "SimpleRefreshManager destroyed");
}

void SimpleRefreshManager::addAccount(const QString& accountId, const QString& username,
                                    const QString& token, const QString& userId,
                                    const QString& proxyHost, int proxyPort,
                                    const QString& proxyType, const QString& proxyUser,
                                    const QString& proxyPass)
{
    // 检查是否已存在
    for (const auto& unit : m_accounts) {
        if (unit.accountId == accountId) {
            NEW_LOG_WARNING(NewLogCategory::SYSTEM, 
                           QString("Account %1 already exists, skipping").arg(accountId));
            return;
        }
    }
    
    AccountUnit unit;
    unit.accountId = accountId;
    
    // 创建固定线程
    unit.thread = new QThread(this);
    unit.thread->setObjectName(QString("RefreshThread_%1").arg(accountId));
    
    // 创建固定Worker
    unit.worker = new AccountWorker(accountId);
    unit.worker->setupAccount(username, "", token, userId);
    unit.worker->setupProxy(proxyHost, proxyPort, proxyType, proxyUser, proxyPass);
    
    // 设置当前参数
    unit.worker->setGameId(m_gameId);
    unit.worker->setPageSize(m_pageSize);
    unit.worker->setPriceFilter(m_priceFilter);
    unit.worker->setFocusFlag(m_focusFlag);
    
    // 移动Worker到专用线程
    unit.worker->moveToThread(unit.thread);
    
    // 连接信号
    connect(unit.worker, &AccountWorker::refreshCompleted,
            this, &SimpleRefreshManager::onAccountCompleted);
    connect(unit.worker, &AccountWorker::refreshError,
            this, &SimpleRefreshManager::onAccountError);
    
    // 启动线程（线程会一直运行，重复使用）
    unit.thread->start();
    
    m_accounts.append(unit);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, 
                QString("Account %1 added to refresh manager").arg(accountId));
}

void SimpleRefreshManager::clearAccounts()
{
    // 停止当前刷新
    stopBatchRefresh();
    
    // 清理所有账号
    for (auto& unit : m_accounts) {
        unit.thread->quit();
        unit.thread->wait(3000);
        delete unit.worker;
        delete unit.thread;
    }
    
    m_accounts.clear();
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "All accounts cleared from refresh manager");
}

void SimpleRefreshManager::startBatchRefresh()
{
    NEW_LOG_DEBUG(NewLogCategory::SYSTEM, QString("🔍 SimpleRefreshManager::startBatchRefresh() 被调用"));
    NEW_LOG_DEBUG(NewLogCategory::SYSTEM, QString("🔍 当前状态 - isRefreshing: %1, isInInterval: %2, 账号数量: %3")
                 .arg(m_isRefreshing ? "true" : "false")
                 .arg(m_isInInterval ? "true" : "false")
                 .arg(m_accounts.size()));

    if (m_isRefreshing || m_isInInterval) {
        NEW_LOG_WARNING(NewLogCategory::SYSTEM, "🔍 刷新已在进行中或在间隔中，跳过");
        return;  // 已在刷新或间隔中
    }

    if (m_accounts.isEmpty()) {
        NEW_LOG_WARNING(NewLogCategory::SYSTEM, "🔍 没有账号可刷新");
        return;  // 没有账号
    }
    
    m_isRefreshing = true;
    m_currentAccountIndex = 0;
    m_completedCount = 0;
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, 
                QString("Starting batch refresh for %1 accounts").arg(m_accounts.size()));
    
    emit refreshProgress(0, m_accounts.size());
    
    // 更新所有账号的参数
    updateAccountParameters();
    
    // 开始刷新第一个账号
    refreshNextAccount();
}

void SimpleRefreshManager::refreshNextAccount()
{
    NEW_LOG_DEBUG(NewLogCategory::SYSTEM, QString("🔍 refreshNextAccount() - 当前索引: %1, 总账号数: %2")
                 .arg(m_currentAccountIndex).arg(m_accounts.size()));

    if (m_currentAccountIndex >= m_accounts.size()) {
        // 所有账号完成
        m_isRefreshing = false;

        NEW_LOG_INFO(NewLogCategory::SYSTEM,
                    QString("🔍 批量刷新完成，处理了 %1 个账号").arg(m_completedCount));

        emit batchRefreshCompleted();
        startInterval();
        return;
    }
    
    // 刷新当前账号
    const AccountUnit& unit = m_accounts[m_currentAccountIndex];

    NEW_LOG_DEBUG(NewLogCategory::ORDER,
                 QString("🔍 准备刷新账号 %1 (%2/%3)")
                 .arg(unit.accountId).arg(m_currentAccountIndex + 1).arg(m_accounts.size()));

    NEW_LOG_DEBUG(NewLogCategory::ORDER,
                 QString("🔍 账号 %1 - Worker指针: %2, 线程: %3")
                 .arg(unit.accountId)
                 .arg(unit.worker ? "有效" : "空")
                 .arg(unit.thread ? "有效" : "空"));

    if (!unit.worker) {
        NEW_LOG_ERROR(NewLogCategory::ORDER, QString("🔍 账号 %1 的Worker为空！").arg(unit.accountId));
        // 跳过这个账号，继续下一个
        m_completedCount++;
        m_currentAccountIndex++;
        emit accountRefreshError(unit.accountId, "Worker为空");
        emit refreshProgress(m_completedCount, m_accounts.size());
        refreshNextAccount();
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("🔍 调用账号 %1 的startRefresh方法").arg(unit.accountId));

    // 在专用线程中执行刷新
    QMetaObject::invokeMethod(unit.worker, "startRefresh", Qt::QueuedConnection);
}

void SimpleRefreshManager::onAccountCompleted(QString accountId, QString result, bool success)
{
    m_completedCount++;
    m_currentAccountIndex++;
    
    NEW_LOG_INFO(NewLogCategory::ORDER, 
                QString("Account %1 refresh completed (%2/%3)")
                .arg(accountId).arg(m_completedCount).arg(m_accounts.size()));
    
    emit accountRefreshCompleted(accountId, result);
    emit refreshProgress(m_completedCount, m_accounts.size());
    
    // 立即刷新下一个账号
    refreshNextAccount();
}

void SimpleRefreshManager::onAccountError(QString accountId, QString error)
{
    m_completedCount++;
    m_currentAccountIndex++;
    
    NEW_LOG_ERROR(NewLogCategory::ORDER, 
                 QString("Account %1 refresh failed: %2 (%3/%4)")
                 .arg(accountId, error).arg(m_completedCount).arg(m_accounts.size()));
    
    emit accountRefreshError(accountId, error);
    emit refreshProgress(m_completedCount, m_accounts.size());
    
    // 即使出错也继续下一个账号
    refreshNextAccount();
}

void SimpleRefreshManager::startInterval()
{
    if (m_intervalSeconds > 0) {
        m_isInInterval = true;
        m_intervalTimer->start(m_intervalSeconds * 1000);
        
        NEW_LOG_INFO(NewLogCategory::SYSTEM, 
                    QString("Starting interval: %1 seconds").arg(m_intervalSeconds));
        
        emit intervalStarted(m_intervalSeconds);
    } else {
        // 无间隔，立即开始下一轮
        NEW_LOG_INFO(NewLogCategory::SYSTEM, "No interval set, starting next round immediately");
        startBatchRefresh();
    }
}

void SimpleRefreshManager::onIntervalTimeout()
{
    m_isInInterval = false;
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "Interval finished, starting next batch refresh");
    
    emit intervalFinished();
    startBatchRefresh();  // 开始下一轮
}

void SimpleRefreshManager::stopBatchRefresh()
{
    if (m_isRefreshing || m_isInInterval) {
        m_isRefreshing = false;
        m_isInInterval = false;
        m_intervalTimer->stop();
        
        NEW_LOG_INFO(NewLogCategory::SYSTEM, "Batch refresh stopped");
    }
}

void SimpleRefreshManager::setInterval(int seconds)
{
    m_intervalSeconds = seconds;
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("Refresh interval set to %1 seconds").arg(seconds));
}

void SimpleRefreshManager::setGameId(const QString& gameId)
{
    m_gameId = gameId;
    updateAccountParameters();
}

void SimpleRefreshManager::setPageSize(int pageSize)
{
    m_pageSize = pageSize;
    updateAccountParameters();
}

void SimpleRefreshManager::setPriceFilter(const QString& priceStr)
{
    m_priceFilter = priceStr;
    updateAccountParameters();
}

void SimpleRefreshManager::setFocusFlag(int focusFlag)
{
    m_focusFlag = focusFlag;
    updateAccountParameters();
}

void SimpleRefreshManager::updateAccountParameters()
{
    // 更新所有账号的参数
    for (const auto& unit : m_accounts) {
        QMetaObject::invokeMethod(unit.worker, [worker = unit.worker, 
                                               gameId = m_gameId, 
                                               pageSize = m_pageSize,
                                               priceFilter = m_priceFilter,
                                               focusFlag = m_focusFlag]() {
            worker->setGameId(gameId);
            worker->setPageSize(pageSize);
            worker->setPriceFilter(priceFilter);
            worker->setFocusFlag(focusFlag);
        }, Qt::QueuedConnection);
    }
}
